/**
 * Financial Projection Table Generator
 * 
 * Generates a comprehensive table showing how different monthly contribution amounts
 * affect the timeline to reach $1.5 million across various market scenarios.
 */

import * as formulas from './formulas';

interface ScenarioData {
  name: string;
  rate: number; // Monthly rate as decimal
  description: string;
}

interface ProjectionResult {
  scenario: string;
  contribution: number;
  months: number;
  date: string;
  deltaVsBaseline: number | null;
}

interface TableRow {
  scenario: string;
  contributionFormatted: string;
  dateHit: string;
  monthsFromNow: number;
  deltaVsBaseline: string;
}

class FinancialProjectionTable {
  private readonly startBalance = 786000; // July 2025 balance
  private readonly target = 1500000; // $1.5M target
  private readonly startDate = new Date(2025, 6, 1); // July 2025 (month 6 = July)
  
  // Market scenarios with their monthly return rates
  private readonly scenarios: ScenarioData[] = [
    {
      name: 'Optimistic',
      rate: 0.0427, // ~4.27%/mo
      description: 'r ≈ 4.27%/mo'
    },
    {
      name: 'Baseline',
      rate: 0.0239, // IRR ≈ 2.39%/mo
      description: 'IRR ≈ 2.39%/mo'
    },
    {
      name: 'Reddit classic',
      rate: Math.pow(1.07, 1/12) - 1, // 7%/yr compounded monthly ≈ 0.565%/mo
      description: '7%/yr ≈ 0.57%/mo'
    },
    {
      name: 'Conservative',
      rate: 0.0069, // σ-discounted 0.69%/mo
      description: 'σ-discounted 0.69%/mo'
    }
  ];

  // Monthly contribution amounts to test
  private readonly contributions = [6250, 5260, 4200];

  /**
   * Calculate months from start date to target date
   */
  private calculateMonthsFromStart(months: number): number {
    return months;
  }

  /**
   * Calculate target date from months
   */
  private calculateTargetDate(months: number): string {
    const targetDate = new Date(this.startDate);
    targetDate.setMonth(targetDate.getMonth() + months);
    
    return targetDate.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long' 
    });
  }

  /**
   * Format contribution amount
   */
  private formatContribution(amount: number): string {
    return `$${amount.toLocaleString()}`;
  }

  /**
   * Format delta vs baseline
   */
  private formatDelta(delta: number | null): string {
    if (delta === null) {
      return '–'; // em-dash for baseline reference
    }
    if (delta === 0) {
      return '0';
    }
    return delta > 0 ? `+${delta}` : `${delta}`;
  }

  /**
   * Calculate all projections
   */
  private calculateProjections(): ProjectionResult[] {
    const results: ProjectionResult[] = [];
    
    for (const scenario of this.scenarios) {
      // Calculate baseline (6250) first to get reference for deltas
      const baselineMonths = formulas.monthsToTarget(
        this.startBalance,
        this.target,
        scenario.rate,
        6250
      );
      
      for (const contribution of this.contributions) {
        const months = formulas.monthsToTarget(
          this.startBalance,
          this.target,
          scenario.rate,
          contribution
        );
        
        const date = this.calculateTargetDate(months);
        const deltaVsBaseline = contribution === 6250 ? null : months - baselineMonths;
        
        results.push({
          scenario: scenario.name,
          contribution,
          months,
          date,
          deltaVsBaseline
        });
      }
    }
    
    return results;
  }

  /**
   * Convert projections to formatted table rows
   */
  private formatTableRows(projections: ProjectionResult[]): TableRow[] {
    return projections.map(proj => ({
      scenario: proj.scenario,
      contributionFormatted: this.formatContribution(proj.contribution),
      dateHit: proj.date,
      monthsFromNow: proj.months,
      deltaVsBaseline: this.formatDelta(proj.deltaVsBaseline)
    }));
  }

  /**
   * Generate the complete financial projection table
   */
  public generateTable(): string {
    const projections = this.calculateProjections();
    const tableRows = this.formatTableRows(projections);
    
    // Group rows by scenario for better organization
    const groupedRows: { [scenario: string]: TableRow[] } = {};
    for (const row of tableRows) {
      if (!groupedRows[row.scenario]) {
        groupedRows[row.scenario] = [];
      }
      groupedRows[row.scenario].push(row);
    }

    let output = '';
    
    // Title and subtitle
    output += 'How much longer does cutting your monthly contribution delay the $1.5 million‑mark?\n';
    output += '(Start point = July 2025 balance $786 k)\n\n';
    
    // Table header
    output += '| Scenario                     | Contribution p mo | Date hit $1.5 M | Months from now | Δ vs $6.25 k |\n';
    output += '|------------------------------|-------------------|------------------|-----------------|---------------|\n';

    // Table rows organized by scenario
    for (const scenario of this.scenarios) {
      const scenarioRows = groupedRows[scenario.name];
      if (!scenarioRows) continue;

      // Sort by contribution amount (descending)
      scenarioRows.sort((a, b) => {
        const aAmount = parseInt(a.contributionFormatted.replace(/[$,]/g, ''));
        const bAmount = parseInt(b.contributionFormatted.replace(/[$,]/g, ''));
        return bAmount - aAmount;
      });

      for (let i = 0; i < scenarioRows.length; i++) {
        const row = scenarioRows[i];
        const scenarioName = i === 0 ? `${scenario.name} (${scenario.description})` : '';

        output += `| ${scenarioName.padEnd(28)} | ${row.contributionFormatted.padEnd(17)} | ${row.dateHit.padEnd(16)} | ${row.monthsFromNow.toString().padEnd(15)} | ${row.deltaVsBaseline.padEnd(13)} |\n`;
      }
    }
    
    return output;
  }

  /**
   * Generate insights summary
   */
  public generateInsights(): string {
    const projections = this.calculateProjections();
    
    // Find specific examples for insights
    const baselineScenario = projections.filter(p => p.scenario === 'Baseline');
    const baseline6250 = baselineScenario.find(p => p.contribution === 6250);
    const baseline5260 = baselineScenario.find(p => p.contribution === 5260);
    const baseline4200 = baselineScenario.find(p => p.contribution === 4200);
    
    let insights = '\n**What the table tells you:**\n\n';
    
    if (baseline6250 && baseline5260) {
      const delta5260 = baseline5260.months - baseline6250.months;
      insights += `• Dropping from $6.25k to $5.26k only pushes the baseline date back ~${delta5260} month${delta5260 !== 1 ? 's' : ''}\n`;
    }
    
    if (baseline6250 && baseline4200) {
      const delta4200 = baseline4200.months - baseline6250.months;
      insights += `• Reducing to $4.2k delays the baseline scenario by ${delta4200} month${delta4200 !== 1 ? 's' : ''}\n`;
    }
    
    // Find the most optimistic vs most conservative scenarios
    const optimisticResults = projections.filter(p => p.scenario === 'Optimistic' && p.contribution === 6250);
    const conservativeResults = projections.filter(p => p.scenario === 'Conservative' && p.contribution === 6250);
    
    if (optimisticResults.length > 0 && conservativeResults.length > 0) {
      const optimisticMonths = optimisticResults[0].months;
      const conservativeMonths = conservativeResults[0].months;
      const scenarioDiff = conservativeMonths - optimisticMonths;
      
      insights += `• Market scenario assumptions matter: Conservative vs Optimistic spans ${scenarioDiff} months difference\n`;
    }
    
    insights += `• The "Reddit classic" 7%/year assumption shows more sensitivity to contribution changes\n`;
    insights += `• Higher contribution rates provide diminishing returns in high-growth scenarios\n`;
    insights += `• In lower-growth scenarios (Reddit classic, Conservative), contribution amounts matter significantly more\n`;

    // Add specific examples from the data
    const redditResults = projections.filter(p => p.scenario === 'Reddit classic');
    const reddit6250 = redditResults.find(p => p.contribution === 6250);
    const reddit4200 = redditResults.find(p => p.contribution === 4200);

    if (reddit6250 && reddit4200) {
      const redditDelta = reddit4200.months - reddit6250.months;
      insights += `• In the Reddit classic scenario, cutting contributions to $4.2k delays the target by ${redditDelta} months\n`;
    }
    
    return insights;
  }

  /**
   * Generate the complete output
   */
  public generateCompleteOutput(): string {
    const table = this.generateTable();
    const insights = this.generateInsights();
    
    return table + insights;
  }
}

// Export the main function
export function generateFinancialProjectionTable(): string {
  const generator = new FinancialProjectionTable();
  return generator.generateCompleteOutput();
}

// If run directly, output the table
if (require.main === module) {
  console.log(generateFinancialProjectionTable());
}
