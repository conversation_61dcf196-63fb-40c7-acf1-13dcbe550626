import {PayslipParser} from "./payslip-parser";
import {WealthTracker} from "./wealth-tracker";
import * as readline from "node:readline";

async function main(): Promise<void> {
  console.log('main: Starting application');
  const tracker = new WealthTracker();
  console.log('main: WealthTracker initialized');
  
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  console.log('main: Readline interface created');

  const question = (query: string): Promise<string> => {
    console.log(`question: Asking query: ${query}`);
    return new Promise(resolve => rl.question(query, resolve));
  };

  const multilineQuestion = (query: string): Promise<string> => {
    console.log(`multilineQuestion: Asking query: ${query}`);
    return new Promise(resolve => {
      console.log(query);
      console.log('(Press Enter twice when done)');
      
      let lines: string[] = [];
      let emptyLineCount = 0;
      
      const onLine = (line: string) => {
        console.log(`multilineQuestion: Received line: ${line}`);
        if (line.trim() === '') {
          emptyLineCount++;
          console.log(`multilineQuestion: Empty line count: ${emptyLineCount}`);
          if (emptyLineCount >= 2) {
            rl.off('line', onLine);
            console.log(`multilineQuestion: Two empty lines, resolving with ${lines.length} lines`);
            resolve(lines.join('\n'));
            return;
          }
        } else {
          emptyLineCount = 0;
        }
        lines.push(line);
      };
      
      rl.on('line', onLine);
    });
  };

  console.log('\n' + '='.repeat(60));
  console.log('PORTFOLIO WEALTH TRACKER');
  console.log('='.repeat(60));
  console.log('Let\'s update your wealth data step by step!');
  console.log('💡 Tip: Leave any field empty to skip and use existing data');

  // Load existing data first
  tracker.loadData();

  try {
    // Step 1: Get month and year (optional)
    const yearInput = await question('\nStep 1: Enter year (e.g., 2025) or press Enter to skip: ');
    console.log(`main: Year input: "${yearInput}"`);
    const monthInput = await question('Step 2: Enter month (1-12) or press Enter to skip: ');
    console.log(`main: Month input: "${monthInput}"`);

    const year = yearInput.trim() ? parseInt(yearInput) : undefined;
    const month = monthInput.trim() ? parseInt(monthInput) : undefined;
    console.log(`main: Parsed year: ${year}, month: ${month}`);

    // Step 2: Get payslips (optional)
    console.log('\nStep 3: Please paste your previous 3 payslips (one at a time)');
    console.log('💡 Press Enter twice without pasting to skip and use existing payslips');

    const payslipTexts: string[] = [];
    for (let i = 1; i <= 3; i++) {
      const payslipText = await multilineQuestion(`\nPaste payslip ${i} (or press Enter twice to skip):`);
      if (payslipText.trim()) {
        payslipTexts.push(payslipText);
        console.log(`main: Payslip ${i} added`);
      } else {
        console.log(`main: Payslip ${i} skipped`);
        break; // Stop asking for more payslips if user skips one
      }
    }

    // Parse payslips and supplement with existing data if needed
    let newPayslipsAdded = 0;
    if (payslipTexts.length > 0) {
      console.log('main: Parsing payslips...');
      const parsedPayslips = PayslipParser.parseMultiplePayslips(payslipTexts);
      console.log(`main: Parsed ${parsedPayslips.length} payslips`);

      for (const payslip of parsedPayslips) {
        if (PayslipParser.validatePayslipData(payslip)) {
          tracker.addPayslip(payslip.date, payslip.gross, payslip.net, payslip.espp, payslip.roth_e, payslip.roth_r);
          console.log(`main: Payslip added to tracker: ${payslip.date}`);
          newPayslipsAdded++;
        } else {
          console.log(`main: Invalid payslip data, skipping: ${payslip.date}`);
        }
      }

      console.log(`✅ Successfully parsed ${newPayslipsAdded} new payslips`);
    } else {
      console.log('📋 No new payslips provided - using existing payslip data for analysis');
    }

    // Check if we have sufficient payslip data for analysis
    const existingPayslips = tracker.getPayslipHistory();
    if (existingPayslips.length === 0) {
      console.log('⚠️  Warning: No payslip data available. Analysis may be limited.');
    } else {
      console.log(`📊 Total payslips available for analysis: ${existingPayslips.length}`);
    }

    // Step 3: Get portfolio data (optional)
    let portfolioDataAdded = false;
    if (year !== undefined && month !== undefined) {
      console.log('\nStep 4: Enter current portfolio values for this month:');
      console.log('💡 Leave any field empty to skip portfolio update and use existing data');

      const trowInput = await question('T.Rowe Retirement (or press Enter to skip): ');
      const robinhoodInput = await question('RobinHood (or press Enter to skip): ');
      const etradeInput = await question('E*Trade (or press Enter to skip): ');
      const teradataInput = await question('Teradata 401k (or press Enter to skip): ');
      const fidelityInput = await question('Fidelity (or press Enter to skip): ');

      const trow = trowInput.trim() ? parseFloat(trowInput) : undefined;
      const robinhood = robinhoodInput.trim() ? parseFloat(robinhoodInput) : undefined;
      const etrade = etradeInput.trim() ? parseFloat(etradeInput) : undefined;
      const teradata = teradataInput.trim() ? parseFloat(teradataInput) : undefined;
      const fidelity = fidelityInput.trim() ? parseFloat(fidelityInput) : undefined;

      console.log(`main: Portfolio values - T.Rowe: ${trow}, RobinHood: ${robinhood}, E*Trade: ${etrade}, Teradata: ${teradata}, Fidelity: ${fidelity}`);

      portfolioDataAdded = tracker.addPortfolioDataIfComplete(year, month, trow, robinhood, etrade, teradata, fidelity);
      if (portfolioDataAdded) {
        console.log('✅ Portfolio data added to tracker');
      }
    } else {
      console.log('\n⏭️  Skipping portfolio data entry - no year/month provided');
    }

    // Check if we have sufficient portfolio data for analysis
    const mostRecentPortfolio = tracker.getMostRecentPortfolioEntry();
    if (!mostRecentPortfolio) {
      console.log('❌ Error: No portfolio data available for analysis');
      return;
    } else if (!portfolioDataAdded) {
      console.log(`📊 Using most recent portfolio data from ${mostRecentPortfolio.date} for analysis`);
    }

    // Step 4: Verify we have sufficient data for analysis
    if (!tracker.hasSufficientDataForAnalysis()) {
      console.log('❌ Error: Insufficient data for analysis. Need at least one portfolio entry and one payslip entry.');
      return;
    }

    // Step 5: Show analysis
    console.log('\nStep 5: Analysis and projections:');
    tracker.analyzeAndProject();
    console.log('main: Analysis and projection complete');

    // Save data
    tracker.saveData();
    console.log('\n✅ All data has been saved!');
    
  } catch (error) {
    console.log('\n❌ An error occurred:', error);
  }
  
  rl.close();
  console.log('main: Readline interface closed');
}

if (require.main === module) {
  main().catch(error => console.error('main: Unhandled error in main function:', error));
}
